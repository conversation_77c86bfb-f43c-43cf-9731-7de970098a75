---
<%*
if (tp.config.run_mode !== 0) {
    new Notice('该模板只能在创建时使用');
    return;
}

var folder_name = tp.config.active_file.basename;
var resource_id = tp.date.now("YYYY-MMDD-HHmmss");
var alias = await tp.system.prompt("请输入别名");
-%>
材料类型: 内容生成
from: "[[<% folder_name %>]]"
aliases: 
  - "<% alias %>"
cdate: <% tp.date.now() %>
---
<%*
// 带格式粘贴
var clipboardItems = await navigator.clipboard.read();
var item = clipboardItems[0];

if (item.types.includes('text/html')) {
    var blob = await item.getType("text/html");
    var html = await blob.text();
    tR += tp.obsidian.htmlToMarkdown(html);
} else if (item.types.includes('text/plain')) {
    var blob = await item.getType("text/plain");
    var text = await blob.text();
    tR += text;
}
%>

<%*
// 获取原文件内容
var sourceFile = tp.config.active_file;
var sourceContent = await app.vault.read(sourceFile);

// 使用正则表达式匹配脚注
var footnoteRegex = /\[\^(\d+)\]/g;
var matches = sourceContent.match(footnoteRegex);
var maxFootnote = 0;

if (matches) {
    matches.forEach(match => {
        var num = parseInt(match.match(/\d+/)[0]);
        maxFootnote = Math.max(maxFootnote, num);
    });
}

var footnote_id = maxFootnote + 1;
var footnote = `[^${footnote_id}]`;
var footnoteContent = `\n${footnote}: [[${resource_id}|${alias}]]`;

// 移动当前文件到资源文件夹
await tp.file.move("References/内容生成/" + resource_id);

// 去掉末尾的换行符后添加脚注
sourceContent = sourceContent.replace(/\n+$/, '');
var updatedContent = sourceContent + footnoteContent;
await app.vault.modify(sourceFile, updatedContent);

// 在原文件光标位置插入脚注引用
tp.file.cursor_append(footnote)
-%>
