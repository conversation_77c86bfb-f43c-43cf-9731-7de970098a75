---
<%*
/*
 * @作者: roam1n
 * @版本: 2.0.0
 * @最后更新: 2025-06-20
 * @功能: 文件内容归档和清理模板 - 简化版本，直接使用模板语法
 */

// 检查是否为新建文件模式
if (tp.config.run_mode !== 0) {
    new Notice('该模板只能在创建时使用');
    return;
}

// 获取当前活动文件（要处理的目标文件）
const targetFile = app.workspace.getActiveFile();
if (!targetFile) {
    new Notice('未找到当前文件');
    return;
}

// 检查是否是模板文件本身
if (targetFile.path.includes('Templater/')) {
    new Notice('不能对模板文件本身执行归档操作');
    return;
}

// 读取目标文件内容
const fileContent = await app.vault.read(targetFile);
if (!fileContent.trim()) {
    new Notice('目标文件为空，无需归档');
    return;
}

// 分离 frontmatter 和正文内容
const frontmatterMatch = fileContent.match(/^---\n([\s\S]*?)\n---\n?([\s\S]*)$/);
let originalFrontmatter = '';
let originalBody = '';

if (frontmatterMatch) {
    originalFrontmatter = frontmatterMatch[1];
    originalBody = frontmatterMatch[2] || '';
} else {
    originalBody = fileContent;
}

if (!originalBody.trim()) {
    new Notice('目标文件正文为空，无需归档');
    return;
}

// 生成归档文件名
const now = new Date();
const archiveFileName = `${now.getFullYear().toString()}-${(now.getMonth() + 1).toString().padStart(2, '0')}${now.getDate().toString().padStart(2, '0')}-${now.getHours().toString().padStart(2, '0')}${now.getMinutes().toString().padStart(2, '0')}-update`;

// 确保归档目录存在
const archiveFolder = app.vault.getAbstractFileByPath('Archive/更新');
if (!archiveFolder) {
    await app.vault.createFolder('Archive/更新');
}

// 清理目标文件正文，保留 frontmatter
let newTargetContent = '';
if (originalFrontmatter.trim()) {
    newTargetContent = `---\n${originalFrontmatter}\n---\n\n`;
}
await app.vault.modify(targetFile, newTargetContent);

// 移动模板文件到归档位置
await tp.file.move(`Archive/更新/${archiveFileName}`);

new Notice(`文件内容已归档到: ${archiveFileName}.md`);
new Notice(`原文件 "${targetFile.basename}" 正文已清理，frontmatter 已保留`);
-%>
更新内容: 内容
from:
  - "[[<% tp.config.active_file.basename %>]]"
---

<%* if (originalFrontmatter?.trim()) { -%>
```yaml
<% originalFrontmatter %>
```
<%* } -%>

<% originalBody %>
