/*
 * @作者: roam1n
 * @版本: 0.2.0
 * @最后更新: 2025-06-24
 * @更新内容: 重构数据渲染逻辑，实现统一项目列表显示，支持已完成/未完成/未计划项目分类
 */

const TODAY = new Date();
TODAY.setHours(0, 0, 0, 0);

const TIME_BLOCK_RATE = {
    '练习': 2,
    '学习': 3,
    '实践': 5,
}

// 工具函数：获取当前大周期
function getCurrentMacroCycle(today) {
    return input?.marco ? dv.page(input?.marco) : dv.pages(`"Nexus/周期"`)
        .sort(p => p.file.name)
        .find(p => {
            const start = new Date(p.开始);
            const done = new Date(p.完成);
            return p.周期数量 > 0 &&
                   today.getTime() >= start.getTime() && 
                   today.getTime() <= done.getTime();
        });
}

// input {marco: 大周期文件名}
const MACRO = getCurrentMacroCycle(TODAY);

if (!MACRO) {
    dv.paragraph("未能获取大周期文件");
    return;
}

// 需要的文件数据：小周期数量及列表
const [MACRO_CODE, MACRO_NAME] = MACRO.file.name.split('=');
const MICRO_COUNT = parseInt(MACRO.周期数量);
const MICRO_LIST = dv.pages(`"Nexus/周期/${MACRO_CODE}"`).sort(p => p.file.name);
const SQUARE_EXP = 6;

// 创建容器
const container = dv.container;
container.id = "macro-cycle-panorama";
container.className = "container";

// 添加CSS样式
const style = document.createElement('style');
style.textContent = `
#macro-cycle-panorama {
    width: 100%;
    font-family: var(--font-interface);
}

.panorama {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.title-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid var(--background-modifier-border);
}

.macro-title {
    font-size: 1.2em;
    font-weight: 600;
    color: var(--text-normal);
    text-decoration: none;
}

.stats-info {
    font-size: 0.9em;
    color: var(--text-muted);
}

.macro-plan {
    display: flex;
    flex-direction: column;
    gap: var(--area-gap);
}

.unified-area {
    background: var(--background-secondary);
    border-radius: 8px;
    border: 1px solid var(--background-modifier-border);
    grid-template-columns: repeat(auto-fit, var(--square-size));
    justify-content: start;
}

.square {
    width: var(--square-size);
    height: var(--square-size);
    background-color: var(--background-primary);
    border: 1px solid var(--background-modifier-border);
    border-radius: 2px;
    position: relative;
    transition: all 0.2s ease;
}

.square:hover {
    transform: scale(1.1);
    z-index: 10;
    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
}

.index-label {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 0.6em;
    color: var(--text-muted);
    font-weight: 500;
    pointer-events: none;
}

.legend {
    margin-top: 16px;
}

.legend-items {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 4px 8px;
    background: var(--background-secondary);
    border-radius: 4px;
    border: 1px solid var(--background-modifier-border);
}

.color-square {
    width: 12px;
    height: 12px;
    border-radius: 2px;
    flex-shrink: 0;
}

.link-text {
    font-size: 0.9em;
    color: var(--text-normal);
    text-decoration: none;
}

.link-text:hover {
    color: var(--text-accent);
}

.stats-text {
    font-size: 0.8em;
    color: var(--text-muted);
    margin-left: 4px;
}
`;
document.head.appendChild(style);

// 动态调整样式函数
function adjustSizesBasedOnContainer() {
    const containerWidth = container.offsetWidth;

    // 根据容器宽度和总方格数计算合适的square大小
    const totalSquares = MICRO_COUNT * (TIME_BLOCK_RATE['学习'] + TIME_BLOCK_RATE['实践'] + TIME_BLOCK_RATE['练习']);
    const columnsPerRow = Math.ceil(Math.sqrt(totalSquares / SQUARE_EXP));

    let squareSize = Math.max(8, (containerWidth - 40) / columnsPerRow);
    let squareGap = Math.max(1, squareSize / 16);
    let areaGap = Math.max(4, squareSize / 4);
    let areaPadding = Math.max(2, squareSize / 8);

    // 更新CSS变量
    container.style.setProperty('--square-size', `${squareSize}px`);
    container.style.setProperty('--square-gap', `${squareGap}px`);
    container.style.setProperty('--area-gap', `${areaGap}px`);
    container.style.setProperty('--area-padding', `${areaPadding}px`);
}

// 初始调整
adjustSizesBasedOnContainer();

// 监听容器大小变化
const resizeObserver = new ResizeObserver(() => {
    adjustSizesBasedOnContainer();
});

resizeObserver.observe(container);

function getFileNameByPath(path) {
    if (!path) return null;
    if (!path?.split) return path;
    return path?.split('/').pop()?.replace('.md', '');
}

// 🗺️ 大周期全景图
const panorama = document.createElement("div");
panorama.className = "panorama";
container.appendChild(panorama);

// 标题区域
const titleSection = document.createElement("div");
titleSection.className = "title-section";
panorama.appendChild(titleSection);

// 添加年份标题
const macroTitle = document.createElement("a");
macroTitle.textContent = `${MACRO_NAME}-全景`;
macroTitle.href = `Nexus/周期/${MACRO.file.name}`;
macroTitle.className = "internal-link macro-title";
macroTitle.dataset['href'] = `Nexus/周期/${MACRO.file.name}`;
titleSection.appendChild(macroTitle);

// 添加统计信息
const statsInfo = document.createElement("div");
statsInfo.textContent = `共${MICRO_COUNT}个周期`;
statsInfo.className = "stats-info";
titleSection.appendChild(statsInfo);

const wishes = {}; // wishes[文件名] = [学习预期, 实践预期, 练习预期, 学习完成, 实践完成, 练习完成]
MACRO.file.tasks.filter(task => task.status === "J").forEach(task => {
    // 匹配模式: 数字-数字-数字 [[文件名]] 或 [[文件名]] 数字-数字-数字
    const pattern = /^\s*(?:(\d+)\s*-\s*(\d+)\s*-\s*(\d+)\s*\[\[(.*?)\]\]|\[\[(.*?)\]\]\s*(\d+)\s*-\s*(\d+)\s*-\s*(\d+))\s*$/;
    const match = task.text.match(pattern);

    if (match) {
        let filename, studyValue, practiceValue, exerciseValue;

        // 根据匹配位置确定数字和文件名
        if (match[1] !== undefined) {
            // 数字在前模式
            filename = match[4];
            studyValue = parseInt(match[1]);
            practiceValue = parseInt(match[2]);
            exerciseValue = parseInt(match[3]);
        } else {
            // 数字在后模式
            filename = match[5];
            studyValue = parseInt(match[6]);
            practiceValue = parseInt(match[7]);
            exerciseValue = parseInt(match[8]);
        }

        // 如果文件名已存在，累加数值；否则初始化
        if (wishes[filename]) {
            wishes[filename][0] += studyValue;    // 累加学习预期
            wishes[filename][1] += practiceValue; // 累加实践预期
            wishes[filename][2] += exerciseValue; // 累加练习预期
        } else {
            wishes[filename] = [studyValue, practiceValue, exerciseValue, 0, 0, 0];
        }
    }
});

// 计算学习、实践、练习的完成数据
MICRO_LIST.forEach(cycle => {
    if (cycle.学习 && cycle.学习.path) {
        const journey = dv.page(cycle.学习.path);
        const wish = getFileNameByPath(journey?.愿望?.path);
        if (wish) {
            wishes[wish][3] += 1;
        }
    }
    if (cycle.实践 && cycle.实践.path) {
        const journey = dv.page(cycle.实践.path);
        const wish = getFileNameByPath(journey?.愿望?.path);
        if (wish) {
            wishes[wish][4] += 1;
        }
    }
    if (cycle.练习 && cycle.练习.path) {
        const journey = dv.page(cycle.练习.path);
        const wish = getFileNameByPath(journey?.愿望?.path);
        if (wish) {
            wishes[wish][5] += 1;
        }
    }
});

// 处理wishes数据，创建统一的项目列表
const projectList = [];

// 1. 已完成项目：从小周期数据中获取
const completedProjects = new Set();
MICRO_LIST.forEach(cycle => {
    ['学习', '实践', '练习'].forEach(type => {
        if (cycle[type] && cycle[type].path) {
            const journey = dv.page(cycle[type].path);
            const wish = getFileNameByPath(journey?.愿望?.path);
            if (wish) {
                completedProjects.add(`${wish}-${type}`);
            }
        }
    });
});

// 2. 未完成项目：从大周期计划中获取所有计划项目，然后减去已完成的项目
Object.keys(wishes).forEach(filename => {
    ['学习', '实践', '练习'].forEach((type, typeIndex) => {
        const planned = wishes[filename][typeIndex]; // 计划数量
        const completed = wishes[filename][typeIndex + 3]; // 完成数量
        const remaining = planned - completed; // 未完成数量

        // 添加已完成项目
        if (completed > 0) {
            projectList.push({
                name: filename,
                type: type,
                status: 'completed',
                count: completed,
                rate: TIME_BLOCK_RATE[type]
            });
        }

        // 添加未完成项目
        if (remaining > 0) {
            projectList.push({
                name: filename,
                type: type,
                status: 'planned',
                count: remaining,
                rate: TIME_BLOCK_RATE[type]
            });
        }
    });
});

// 3. 计算未计划项目：根据周期总长度和时间块比例计算剩余可用时间块
const totalTimeBlocks = {
    '学习': MICRO_COUNT * TIME_BLOCK_RATE['学习'],
    '实践': MICRO_COUNT * TIME_BLOCK_RATE['实践'],
    '练习': MICRO_COUNT * TIME_BLOCK_RATE['练习']
};

const usedTimeBlocks = {
    '学习': 0,
    '实践': 0,
    '练习': 0
};

// 计算已使用的时间块
Object.keys(wishes).forEach(filename => {
    usedTimeBlocks['学习'] += wishes[filename][0] * TIME_BLOCK_RATE['学习'];
    usedTimeBlocks['实践'] += wishes[filename][1] * TIME_BLOCK_RATE['实践'];
    usedTimeBlocks['练习'] += wishes[filename][2] * TIME_BLOCK_RATE['练习'];
});

// 添加未计划项目
['学习', '实践', '练习'].forEach(type => {
    const remaining = totalTimeBlocks[type] - usedTimeBlocks[type];
    if (remaining > 0) {
        projectList.push({
            name: '未计划',
            type: type,
            status: 'unplanned',
            count: Math.ceil(remaining / TIME_BLOCK_RATE[type]), // 转换为项目数量
            rate: TIME_BLOCK_RATE[type]
        });
    }
});

// 按状态和类型排序：已完成 -> 未完成 -> 未计划，同状态内按学习->实践->练习排序
const statusOrder = { 'completed': 0, 'planned': 1, 'unplanned': 2 };
const typeOrder = { '学习': 0, '实践': 1, '练习': 2 };

projectList.sort((a, b) => {
    if (statusOrder[a.status] !== statusOrder[b.status]) {
        return statusOrder[a.status] - statusOrder[b.status];
    }
    if (typeOrder[a.type] !== typeOrder[b.type]) {
        return typeOrder[a.type] - typeOrder[b.type];
    }
    return a.name.localeCompare(b.name);
});

// 为了保持图例兼容性，创建wishesArray
const wishesArray = Object.keys(wishes).map(filename => {
    return {
        name: filename,
        学习:   [wishes[filename][0], wishes[filename][3]], // 学习总数 / 学习完成数量（深色）
        实践: [wishes[filename][1], wishes[filename][4]], // 实践总数 / 实践完成数量（深色）
        练习:  [wishes[filename][2], wishes[filename][5]], // 练习总数 / 练习完成数量（深色）
    };
}).sort((a, b) => {
    // 先以学习总数排序，相同则以实践总数排序，类推练习总数
    if (a.学习[0] !== b.学习[0]) {
        return b.学习[0] - a.学习[0];
    } else if (a.实践[0] !== b.实践[0]) {
        return b.实践[0] - a.实践[0];
    } else {
        return b.练习[0] - a.练习[0];
    }
});

// 创建颜色组合
const colorCombinations = [
    { light: 'rgba(229, 115, 115, 0.6)', dark: 'rgba(198, 40, 40, 0.8)' }, // 红色
    { light: 'rgba(129, 199, 132, 0.6)', dark: 'rgba(46, 125, 50, 0.8)' }, // 绿色
    { light: 'rgba(121, 134, 203, 0.6)', dark: 'rgba(40, 53, 147, 0.8)' }, // 蓝色
    { light: 'rgba(255, 213, 79, 0.5)', dark: 'rgba(249, 168, 37, 0.7)' }, // 黄色
    { light: 'rgba(186, 104, 200, 0.6)', dark: 'rgba(123, 31, 162, 0.8)' }, // 紫色
    { light: 'rgba(77, 208, 225, 0.6)', dark: 'rgba(0, 131, 143, 0.8)' }, // 青色
    { light: 'rgba(255, 183, 77, 0.6)', dark: 'rgba(239, 108, 0, 0.8)' }, // 橙色
    { light: 'rgba(149, 117, 205, 0.6)', dark: 'rgba(81, 45, 168, 0.8)' }  // 淡紫色
];

// 创建全景规划的容器
const macroPlan = document.createElement("div");
macroPlan.className = "macro-plan";
panorama.appendChild(macroPlan);

// 创建统一的项目显示区域
const unifiedArea = document.createElement("div");
unifiedArea.className = "unified-area";
unifiedArea.style.display = "grid";
unifiedArea.style.gridTemplateRows = `repeat(${SQUARE_EXP}, 1fr)`;
unifiedArea.style.gap = "var(--square-gap)";
unifiedArea.style.padding = "var(--area-padding)";
macroPlan.appendChild(unifiedArea);

// 计算总的方格数量
const totalSquares = MICRO_COUNT * (TIME_BLOCK_RATE['学习'] + TIME_BLOCK_RATE['实践'] + TIME_BLOCK_RATE['练习']);
const squares = [];

// 生成所有方格
for (let i = 0; i < totalSquares; i++) {
    const square = document.createElement('div');
    square.className = "square";
    unifiedArea.appendChild(square);

    // 添加索引标签 (每隔一定数量显示一次)
    if ((i + 1) % Math.ceil(totalSquares / (SQUARE_EXP * 2)) === 0) {
        const indexLabel = document.createElement("div");
        indexLabel.textContent = Math.ceil((i + 1) / 10).toString();
        indexLabel.className = "index-label";
        square.appendChild(indexLabel);
    }

    squares.push(square);
}

// 新的填色逻辑：基于统一项目列表
function fillUnifiedSquares() {
    let currentSquareIndex = 0;
    const projectColorMap = new Map(); // 用于为每个项目名称分配颜色
    let colorIndex = 0;

    projectList.forEach(project => {
        // 为新项目分配颜色
        if (!projectColorMap.has(project.name)) {
            projectColorMap.set(project.name, colorIndex % colorCombinations.length);
            colorIndex++;
        }

        const projectColorIndex = projectColorMap.get(project.name);
        const totalSquares = project.count * project.rate;

        // 填充方格
        for (let i = 0; i < totalSquares; i++) {
            if (currentSquareIndex < squares.length) {
                const square = squares[currentSquareIndex];

                // 根据项目状态选择颜色
                if (project.status === 'completed') {
                    square.style.backgroundColor = colorCombinations[projectColorIndex].dark;
                } else if (project.status === 'planned') {
                    square.style.backgroundColor = colorCombinations[projectColorIndex].light;
                } else if (project.status === 'unplanned') {
                    square.style.backgroundColor = 'rgba(158, 158, 158, 0.3)';
                }

                // 设置提示信息
                const statusText = project.status === 'completed' ? '已完成' :
                                 project.status === 'planned' ? '未完成' : '未计划';
                square.title = `${project.name} (${project.type}): ${statusText} ${i + 1}/${totalSquares}`;

                currentSquareIndex++;
            }
        }
    });
}

// 执行填色
fillUnifiedSquares();

// 创建图例
const legend = document.createElement('div');
legend.className = "legend";
panorama.appendChild(legend);

// 创建图例项
const legendItems = document.createElement('div');
legendItems.className = "legend-items";
legend.appendChild(legendItems);

wishesArray.forEach((wish, index) => {
    const colorIndex = index % colorCombinations.length;
    const legendItem = document.createElement('div');
    legendItem.className = "legend-item";
    
    const colorSquare = document.createElement('div');
    colorSquare.className = "color-square";
    colorSquare.style.backgroundColor = colorCombinations[colorIndex].dark;
    legendItem.appendChild(colorSquare);

    const linkText = document.createElement('a');
    linkText.className = 'internal-link link-text';
    linkText.innerText = wish.name;
    linkText.dataset['href'] = wish.name;
    linkText.href = wish.name;
    legendItem.appendChild(linkText);
    
    // 添加统计信息
    const statsText = document.createElement('span');
    statsText.className = "stats-text";
    statsText.textContent = `${wish.学习[0]}-${wish.实践[0]}-${wish.练习[0]}`;
    legendItem.appendChild(statsText);

    legendItems.appendChild(legendItem);
});
